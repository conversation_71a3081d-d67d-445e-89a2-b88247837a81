<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;
use BeyondCode\Comments\Traits\HasComments;
use Cog\Laravel\Love\Reactable\Models\Traits\Reactable;
use Cog\Contracts\Love\Reactable\Models\Reactable as ReactableInterface;

class Article extends Model implements ReactableInterface
{
    use HasSlug, HasTags, HasComments, Reactable;

    protected $fillable = [
        'user_id',
        'category_id',
        'title',
        'slug',
        'excerpt',
        'content',
        'cover_image',
        'status',
        'rejection_reason',
        'read_time',
        'is_featured',
        'allow_comments',
        'published_at'
    ];

    protected $casts = [
        'content' => 'json',
        'is_featured' => 'boolean',
        'allow_comments' => 'boolean',
        'published_at' => 'datetime',
    ];

    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(ArticleCategory::class, 'category_id');
    }

    public function views()
    {
        return $this->hasMany(ArticleView::class);
    }

    public function scopePublished($query)
    {
        return $query->where('status', 'published')
            ->whereNotNull('published_at')
            ->where('published_at', '<=', now());
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function incrementViewCount()
    {
        $this->increment('views_count');
    }

    public function calculateReadTime()
    {
        $wordsPerMinute = 200;
        $wordCount = str_word_count(strip_tags($this->getContentAsText()));
        return ceil($wordCount / $wordsPerMinute);
    }

    protected function getContentAsText()
    {
        // Конвертация EditorJS blocks в текст
        $text = '';
        foreach ($this->content['blocks'] ?? [] as $block) {
            if ($block['type'] === 'paragraph' || $block['type'] === 'header') {
                $text .= strip_tags($block['data']['text'] ?? '') . ' ';
            }
        }
        return $text;
    }
}
